// Contact Form Handler
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contactForm');
    
    // Load saved data on page load
    loadSavedData();
    
    // Handle form submission
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        const formData = {
            yourName: document.getElementById('yourName').value,
            emailAddress: document.getElementById('emailAddress').value,
            companyName: document.getElementById('companyName').value,
            website: document.getElementById('website').value,
            message: document.getElementById('message').value,
            timestamp: new Date().toISOString()
        };
        
        // Save to localStorage
        saveContactData(formData);
        
        // Show success message
        showSuccessMessage();
        
        // Reset form
        contactForm.reset();
    });
    
    // Auto-save data as user types
    const formInputs = contactForm.querySelectorAll('input, textarea');
    formInputs.forEach(input => {
        input.addEventListener('input', function() {
            autoSaveData();
        });
    });
});

function saveContactData(data) {
    try {
        // Get existing contacts from localStorage
        let contacts = JSON.parse(localStorage.getItem('weatherAppContacts')) || [];
        
        // Add new contact
        contacts.push(data);
        
        // Save back to localStorage
        localStorage.setItem('weatherAppContacts', JSON.stringify(contacts));
        
        console.log('Contact data saved successfully:', data);
    } catch (error) {
        console.error('Error saving contact data:', error);
    }
}

function autoSaveData() {
    try {
        const formData = {
            yourName: document.getElementById('yourName').value,
            emailAddress: document.getElementById('emailAddress').value,
            companyName: document.getElementById('companyName').value,
            website: document.getElementById('website').value,
            message: document.getElementById('message').value
        };
        
        // Save current form state (draft)
        localStorage.setItem('weatherAppContactDraft', JSON.stringify(formData));
    } catch (error) {
        console.error('Error auto-saving data:', error);
    }
}

function loadSavedData() {
    try {
        // Load draft data if exists
        const draftData = localStorage.getItem('weatherAppContactDraft');
        if (draftData) {
            const data = JSON.parse(draftData);
            
            document.getElementById('yourName').value = data.yourName || '';
            document.getElementById('emailAddress').value = data.emailAddress || '';
            document.getElementById('companyName').value = data.companyName || '';
            document.getElementById('website').value = data.website || '';
            document.getElementById('message').value = data.message || '';
        }
    } catch (error) {
        console.error('Error loading saved data:', error);
    }
}

function showSuccessMessage() {
    // Create success alert
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
    alertDiv.innerHTML = `
        <strong>Success!</strong> Your message has been sent successfully.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // Insert after form
    const form = document.getElementById('contactForm');
    form.parentNode.insertBefore(alertDiv, form.nextSibling);
    
    // Remove draft data after successful submission
    localStorage.removeItem('weatherAppContactDraft');
    
    // Auto-remove alert after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Function to get all saved contacts (for debugging/admin purposes)
function getAllContacts() {
    try {
        const contacts = JSON.parse(localStorage.getItem('weatherAppContacts')) || [];
        console.log('All saved contacts:', contacts);
        return contacts;
    } catch (error) {
        console.error('Error retrieving contacts:', error);
        return [];
    }
}

// Function to clear all contacts (for debugging/admin purposes)
function clearAllContacts() {
    try {
        localStorage.removeItem('weatherAppContacts');
        localStorage.removeItem('weatherAppContactDraft');
        console.log('All contact data cleared');
    } catch (error) {
        console.error('Error clearing contacts:', error);
    }
}
