body {
  margin: 0;
  padding: 0;
  background-color: #1E202B;
}

nav {
  background-color: #1E202B;
}

.logo-type {
  display: inline-block;
  vertical-align: middle;
}
.site-title {
  font-size: 1rem;
}

.site-description {
  color: #bfc1c8;
  font-size: 0.7rem;
}

#navbarSupportedContent{
  margin-left: 13rem;
}

.nav-item {
  margin: 0 15px;
}

.nav-item a{
  padding-left: 2rem;
  padding-right: 1rem;
}

.navbar-brand img {
  margin-left: -50px;
}










.hero-section {
  min-height: 50vh;
  background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('../images/banner.png');
  background-position: center;
  background-size: cover;
  padding-bottom: 130px;

}

#findLocation{
  padding: 15px 30px;
  background: rgb(30, 32, 43);
  border-radius: 3rem;
  border-color: transparent;
  color: white;

}
#find{
  border-radius: 3rem; 
  padding: 10px 40px;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}
#findLocation::placeholder{
color: gray;
font-weight: bold;

}
.input-group>.form-control:focus{
  z-index: 0;
  box-shadow: none;
}








footer {
  background-color: #262936;
  padding: 30px 0;

}

.bg-heading{
  background-color: #2D303D;

}
.w-20{
  width: 20px;
}
.bg-custom{
  background-color: #323544;
}
.bg-custom-two{
  background-color: #262936;
}
.navbar-nav .nav-link{
  padding: 5px 15px;
}
.navbar-nav .nav-link.active{
  color:#0B5ED7;
  padding: 5px 15px;
  border:1px solid #0B5ED7;
  border-radius: 20px;
}
.forecast-card{
  padding: 20px 20px 60px;
}

/* Newsletter Footer Styles */
.newsletter-input {
  background-color: #3a3d4a;
  border: none;
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
}

.newsletter-input::placeholder {
  color: #bfc1c8;
}

.newsletter-input:focus {
  background-color: #3a3d4a;
  border: none;
  box-shadow: none;
  color: white;
}

.newsletter-btn {
  background-color: #007bff;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-weight: 500;
}

.social-icons .social-icon {
  display: inline-block;
  width: 40px;
  height: 40px;
  background-color: #007bff;
  color: white;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  text-decoration: none;
  transition: background-color 0.3s;
}

.social-icon:hover {
  background-color: #0056b3;
  color: white;
}
