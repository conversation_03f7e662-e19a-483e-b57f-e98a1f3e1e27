body {
  margin: 0;
  padding: 0;
  background-color: #1E202B;
}

nav {
  background-color: #1E202B;
}

.logo-type {
  display: inline-block;
  vertical-align: middle;
}
.site-title {
  font-size: 1rem;
}

.site-description {
  color: #bfc1c8;
  font-size: 0.7rem;
}

#navbarSupportedContent{
  margin-left: 13rem;
}

.nav-item {
  margin: 0 15px;
}

.nav-item a{
  padding-left: 2rem;
  padding-right: 1rem;
}

.navbar-brand img {
  margin-left: -50px;
}










.hero-section {
  min-height: 50vh;
  background-image: linear-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.3)), url('../images/banner.png');
  background-position: center;
  background-size: cover;
  padding-bottom: 130px;

}

#findLocation{
  padding: 15px 30px;
  background: rgb(30, 32, 43);
  border-radius: 3rem;
  border-color: transparent;
  color: white;

}
#find{
  border-radius: 3rem; 
  padding: 10px 40px;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
}
#findLocation::placeholder{
color: gray;
font-weight: bold;

}
.input-group>.form-control:focus{
  z-index: 0;
  box-shadow: none;
}








footer {
  background-color: #262936;
  padding: 30px 0;

}

.bg-heading{
  background-color: #2D303D;

}
.w-20{
  width: 20px;
}
.bg-custom{
  background-color: #323544;
}
.bg-custom-two{
  background-color: #262936;
}
.navbar-nav .nav-link{
  padding: 5px 15px;
}
.navbar-nav .nav-link.active{
  color:#0B5ED7;
  padding: 5px 15px;
  border:1px solid #0B5ED7;
  border-radius: 20px;
}
.forecast-card{
  padding: 20px 20px 60px;
}


.newsletter-input {
  background-color: #3a3d4a;
  border: none;
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
}

.newsletter-input::placeholder {
  color: #bfc1c8;
}

.newsletter-input:focus {
  background-color: #3a3d4a;
  border: none;
  box-shadow: none;
  color: white;
}

.newsletter-btn {
  background-color: #007bff;
  border: none;
  padding: 12px 30px;
  border-radius: 25px;
  font-weight: 500;
}

.social-icons .social-icon {
  display: inline-block;
  width: 40px;
  height: 40px;
  background-color:transparent;
  color: #0056b3;
  text-align: center;
  line-height: 40px;
  border-radius: 50%;
  text-decoration: none;
  transition: background-color 0.3s;
  border: #0056b3 1px solid;
}

.social-icon:hover {
  background-color: #0056b3;
  color: white;
}




/* Contact Page Styles */
.breadcrumb {
  background:#262936 ;
  border-radius: 50px;
  padding: 10px 20px;
}

.breadcrumb-item a {
  color: #bfc1c8;
  text-decoration: none;
}

.breadcrumb-item a:hover {
  color: white;
}

.contact-info {
  background-color: #323544;
  border-radius: 8px;
}

.contact-item {
  display: flex;
  align-items: flex-start;
}

.contact-icon {
  color: #007bff;
  font-size: 1.2rem;
  margin-right: 15px;
  margin-top: 5px;
  min-width: 20px;
}

.contact-details h6 {
  font-size: 1rem;
  font-weight: 600;
  color: white;
}

.contact-details p {
  color: white;
}

.contact-form-section {
  
  padding: 2rem;
  border-radius: 8px;
}

.contact-input {
  background-color: #3a3d4a;
  border: px solid #4a4d5a;
  color: white;
  padding: 12px 15px;
  border-radius: 50px;
  margin-bottom: 1rem;
}

.contact-input::placeholder {
  color: #bfc1c8;
}

.contact-input:focus {
  background-color: #3a3d4a;
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  color: white;
}

.contact-submit-btn {
  background-color: #007bff;
  border: none;
  padding: 12px 30px;
  font-weight: 500;
  transition: background-color 0.3s;
  border-radius: 50px;
}

.contact-submit-btn:hover {
  background-color: #0056b3;
}


body h1, body h2, body h3, body h4, body h5, body h6 {
  color: white;
}

body p, body span, body div {
  color: white;
}


.text-muted {
  color: #bfc1c8 !important;
}
