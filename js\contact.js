
document.addEventListener('DOMContentLoaded', function() {
    const contactForm = document.getElementById('contactForm');
    
    
    loadSavedData();
    
    
    contactForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        
        const formData = {
            yourName: document.getElementById('yourName').value,
            emailAddress: document.getElementById('emailAddress').value,
            companyName: document.getElementById('companyName').value,
            website: document.getElementById('website').value,
            message: document.getElementById('message').value,
            timestamp: new Date().toISOString()
        };
        
        
        saveContactData(formData);
        
        
        showSuccessMessage();
        
        
        contactForm.reset();
    });
    
    
    const formInputs = contactForm.querySelectorAll('input, textarea');
    formInputs.forEach(input => {
        input.addEventListener('input', function() {
            autoSaveData();
        });
    });
});

function saveContactData(data) {
    try {
        
        let contacts = JSON.parse(localStorage.getItem('weatherAppContacts')) || [];
        
        
        contacts.push(data);
        
        
        localStorage.setItem('weatherAppContacts', JSON.stringify(contacts));
        
        console.log('Contact data saved successfully:', data);
    } catch (error) {
        console.error('Error saving contact data:', error);
    }
}

function autoSaveData() {
    try {
        const formData = {
            yourName: document.getElementById('yourName').value,
            emailAddress: document.getElementById('emailAddress').value,
            companyName: document.getElementById('companyName').value,
            website: document.getElementById('website').value,
            message: document.getElementById('message').value
        };
        
        
        localStorage.setItem('weatherAppContactDraft', JSON.stringify(formData));
    } catch (error) {
        console.error('Error auto-saving data:', error);
    }
}

function loadSavedData() {
    try {
        
        const draftData = localStorage.getItem('weatherAppContactDraft');
        if (draftData) {
            const data = JSON.parse(draftData);
            
            document.getElementById('yourName').value = data.yourName || '';
            document.getElementById('emailAddress').value = data.emailAddress || '';
            document.getElementById('companyName').value = data.companyName || '';
            document.getElementById('website').value = data.website || '';
            document.getElementById('message').value = data.message || '';
        }
    } catch (error) {
        console.error('Error loading saved data:', error);
    }
}

function showSuccessMessage() {
    
    const alertDiv = document.createElement('div');
    alertDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
    alertDiv.innerHTML = `
        <strong>Success!</strong> Your message has been sent successfully.
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    
    const form = document.getElementById('contactForm');
    form.parentNode.insertBefore(alertDiv, form.nextSibling);
    
    localStorage.removeItem('weatherAppContactDraft');
    
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}


function getAllContacts() {
    try {
        const contacts = JSON.parse(localStorage.getItem('weatherAppContacts')) || [];
        console.log('All saved contacts:', contacts);
        return contacts;
    } catch (error) {
        console.error('Error retrieving contacts:', error);
        return [];
    }
}


function clearAllContacts() {
    try {
        localStorage.removeItem('weatherAppContacts');
        localStorage.removeItem('weatherAppContactDraft');
        console.log('All contact data cleared');
    } catch (error) {
        console.error('Error clearing contacts:', error);
    }
}
